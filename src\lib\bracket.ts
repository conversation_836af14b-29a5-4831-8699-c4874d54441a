import { BracketType, Match, Player, BracketDocument } from '@/types/bracket'
// Generate unique ID without external dependency
function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export function generateBracket(
  players: string[],
  type: BracketType,
  name: string
): BracketDocument {
  const playerObjects: Player[] = players.map((playerName, index) => ({
    id: generateId(),
    name: playerN<PERSON>,
    seed: index + 1
  }))

  const matches = type === 'single' 
    ? generateSingleEliminationMatches(playerObjects)
    : generateDoubleEliminationMatches(playerObjects)

  return {
    name,
    type,
    players: playerObjects,
    matches,
    currentRound: 1,
    isComplete: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

function generateSingleEliminationMatches(players: Player[]): Match[] {
  const matches: Match[] = []
  const playerCount = players.length
  const rounds = Math.ceil(Math.log2(playerCount))
  
  // Calculate total matches needed
  const totalMatches = playerCount - 1
  
  // Create first round matches
  const firstRoundMatches = Math.floor(playerCount / 2)
  let matchId = 1
  
  for (let i = 0; i < firstRoundMatches; i++) {
    const player1 = players[i * 2]
    const player2 = players[i * 2 + 1]
    
    matches.push({
      id: `match-${matchId}`,
      round: 1,
      position: i + 1,
      player1,
      player2,
      status: 'pending'
    })
    matchId++
  }
  
  // Handle odd number of players (give bye to last player)
  if (playerCount % 2 === 1) {
    const lastPlayer = players[playerCount - 1]
    matches.push({
      id: `match-${matchId}`,
      round: 1,
      position: firstRoundMatches + 1,
      player1: lastPlayer,
      status: 'completed',
      winner: lastPlayer
    })
    matchId++
  }
  
  // Create subsequent rounds
  for (let round = 2; round <= rounds; round++) {
    const previousRoundMatches = matches.filter(m => m.round === round - 1)
    const thisRoundMatches = Math.ceil(previousRoundMatches.length / 2)
    
    for (let i = 0; i < thisRoundMatches; i++) {
      matches.push({
        id: `match-${matchId}`,
        round,
        position: i + 1,
        status: 'pending'
      })
      matchId++
    }
  }
  
  // Link matches for progression
  linkSingleEliminationMatches(matches)
  
  return matches
}

function generateDoubleEliminationMatches(players: Player[]): Match[] {
  const matches: Match[] = []
  const playerCount = players.length
  const winnersRounds = Math.ceil(Math.log2(playerCount))
  const losersRounds = (winnersRounds - 1) * 2
  
  let matchId = 1
  
  // Generate winners bracket matches
  const winnersMatches = generateSingleEliminationMatches(players)
  winnersMatches.forEach(match => {
    matches.push({
      ...match,
      id: `w-${matchId}`,
      isWinnersMatch: true
    })
    matchId++
  })
  
  // Generate losers bracket matches
  for (let round = 1; round <= losersRounds; round++) {
    const matchesInRound = calculateLosersRoundMatches(round, playerCount)
    
    for (let i = 0; i < matchesInRound; i++) {
      matches.push({
        id: `l-${matchId}`,
        round,
        position: i + 1,
        status: 'pending',
        isWinnersMatch: false
      })
      matchId++
    }
  }
  
  // Add grand finals matches
  matches.push({
    id: `gf-1`,
    round: winnersRounds + 1,
    position: 1,
    status: 'pending',
    isWinnersMatch: true
  })
  
  // Add potential grand finals reset
  matches.push({
    id: `gf-2`,
    round: winnersRounds + 2,
    position: 1,
    status: 'pending',
    isWinnersMatch: true
  })
  
  linkDoubleEliminationMatches(matches, winnersRounds, losersRounds)
  
  return matches
}

function linkSingleEliminationMatches(matches: Match[]): void {
  const rounds = Math.max(...matches.map(m => m.round))
  
  for (let round = 1; round < rounds; round++) {
    const currentRoundMatches = matches.filter(m => m.round === round)
    const nextRoundMatches = matches.filter(m => m.round === round + 1)
    
    for (let i = 0; i < currentRoundMatches.length; i += 2) {
      const nextMatchIndex = Math.floor(i / 2)
      const nextMatch = nextRoundMatches[nextMatchIndex]
      
      if (nextMatch) {
        currentRoundMatches[i].nextMatchId = nextMatch.id
        currentRoundMatches[i].nextMatchPosition = 'player1'
        
        if (currentRoundMatches[i + 1]) {
          currentRoundMatches[i + 1].nextMatchId = nextMatch.id
          currentRoundMatches[i + 1].nextMatchPosition = 'player2'
        }
      }
    }
  }
}

function linkDoubleEliminationMatches(
  matches: Match[], 
  winnersRounds: number, 
  losersRounds: number
): void {
  // Link winners bracket matches
  const winnersMatches = matches.filter(m => m.isWinnersMatch && m.round <= winnersRounds)
  linkSingleEliminationMatches(winnersMatches)
  
  // Link losers bracket matches (simplified - would need more complex logic for full implementation)
  const losersMatches = matches.filter(m => m.isWinnersMatch === false)
  // This would require more complex linking logic for a full double elimination bracket
}

function calculateLosersRoundMatches(round: number, playerCount: number): number {
  // Simplified calculation - would need more complex logic for accurate double elimination
  const firstRoundMatches = Math.floor(playerCount / 2)
  if (round === 1) return firstRoundMatches
  return Math.max(1, Math.floor(firstRoundMatches / Math.pow(2, round - 1)))
}

export function updateMatch(
  matches: Match[],
  matchId: string,
  winner: Player,
  score?: { player1: number; player2: number }
): Match[] {
  const updatedMatches = [...matches]
  const matchIndex = updatedMatches.findIndex(m => m.id === matchId)
  
  if (matchIndex === -1) return matches
  
  const match = updatedMatches[matchIndex]
  match.winner = winner
  match.status = 'completed'
  if (score) match.score = score
  
  // Progress winner to next match
  if (match.nextMatchId) {
    const nextMatchIndex = updatedMatches.findIndex(m => m.id === match.nextMatchId)
    if (nextMatchIndex !== -1) {
      const nextMatch = updatedMatches[nextMatchIndex]
      if (match.nextMatchPosition === 'player1') {
        nextMatch.player1 = winner
      } else {
        nextMatch.player2 = winner
      }
    }
  }
  
  return updatedMatches
}

export function canStartMatch(match: Match): boolean {
  return match.status === 'pending' && !!match.player1 && !!match.player2
}

export function getBracketWinner(matches: Match[]): Player | null {
  const finalMatches = matches.filter(m => !m.nextMatchId)
  const completedFinals = finalMatches.filter(m => m.status === 'completed')
  
  if (completedFinals.length === 0) return null
  
  // For single elimination, just return the winner of the final match
  // For double elimination, this would need more complex logic
  return completedFinals[0].winner || null
}

export function isBracketComplete(matches: Match[]): boolean {
  const finalMatches = matches.filter(m => !m.nextMatchId)
  return finalMatches.every(m => m.status === 'completed')
}
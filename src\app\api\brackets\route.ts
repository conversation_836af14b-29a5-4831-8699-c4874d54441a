import { NextRequest, NextResponse } from 'next/server'
import { ObjectId } from 'mongodb'
import { getDatabase } from '@/lib/mongodb'
import { BracketDocument, BracketResponse, CreateBracketRequest } from '@/types/bracket'
import { COLLECTIONS } from '@/types/database'
import { generateBracket } from '@/lib/bracket'

export async function GET() {
  try {
    const db = await getDatabase()
    const brackets = await db.collection<BracketDocument>(COLLECTIONS.BRACKETS)
      .find({})
      .sort({ createdAt: -1 })
      .toArray()

    const response: BracketResponse[] = brackets.map(bracket => ({
      id: bracket._id!.toString(),
      name: bracket.name,
      type: bracket.type,
      players: bracket.players,
      matches: bracket.matches,
      currentRound: bracket.currentRound,
      isComplete: bracket.isComplete,
      winner: bracket.winner,
      createdAt: bracket.createdAt!.toISOString(),
      updatedAt: bracket.updatedAt!.toISOString()
    }))

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching brackets:', error)
    return NextResponse.json(
      { error: 'Failed to fetch brackets' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateBracketRequest = await request.json()
    
    if (!body.name || !body.type || !body.players || body.players.length < 2) {
      return NextResponse.json(
        { error: 'Invalid bracket data. Name, type, and at least 2 players are required.' },
        { status: 400 }
      )
    }

    if (!['single', 'double'].includes(body.type)) {
      return NextResponse.json(
        { error: 'Invalid bracket type. Must be "single" or "double".' },
        { status: 400 }
      )
    }

    const bracket = generateBracket(body.players, body.type, body.name)
    
    const db = await getDatabase()
    const result = await db.collection<BracketDocument>(COLLECTIONS.BRACKETS)
      .insertOne(bracket)

    const response: BracketResponse = {
      id: result.insertedId.toString(),
      name: bracket.name,
      type: bracket.type,
      players: bracket.players,
      matches: bracket.matches,
      currentRound: bracket.currentRound,
      isComplete: bracket.isComplete,
      winner: bracket.winner,
      createdAt: bracket.createdAt!.toISOString(),
      updatedAt: bracket.updatedAt!.toISOString()
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Error creating bracket:', error)
    return NextResponse.json(
      { error: 'Failed to create bracket' },
      { status: 500 }
    )
  }
}
import { NextRequest, NextResponse } from 'next/server'
import { ObjectId } from 'mongodb'
import { getDatabase } from '@/lib/mongodb'
import { BracketDocument, BracketResponse, UpdateMatchRequest } from '@/types/bracket'
import { COLLECTIONS } from '@/types/database'
import { updateMatch, getBracketWinner, isBracketComplete } from '@/lib/bracket'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const db = await getDatabase()
    const bracket = await db.collection<BracketDocument>(COLLECTIONS.BRACKETS)
      .findOne({ _id: new ObjectId(params.id) })

    if (!bracket) {
      return NextResponse.json(
        { error: 'Bracket not found' },
        { status: 404 }
      )
    }

    const response: BracketResponse = {
      id: bracket._id!.toString(),
      name: bracket.name,
      type: bracket.type,
      players: bracket.players,
      matches: bracket.matches,
      currentRound: bracket.currentRound,
      isComplete: bracket.isComplete,
      winner: bracket.winner,
      createdAt: bracket.createdAt!.toISOString(),
      updatedAt: bracket.updatedAt!.toISOString()
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching bracket:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bracket' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body: UpdateMatchRequest = await request.json()
    
    if (!body.matchId || !body.winner) {
      return NextResponse.json(
        { error: 'Match ID and winner are required' },
        { status: 400 }
      )
    }

    const db = await getDatabase()
    const bracket = await db.collection<BracketDocument>(COLLECTIONS.BRACKETS)
      .findOne({ _id: new ObjectId(params.id) })

    if (!bracket) {
      return NextResponse.json(
        { error: 'Bracket not found' },
        { status: 404 }
      )
    }

    // Find the winner player object
    const winnerPlayer = bracket.players.find(p => p.id === body.winner)
    if (!winnerPlayer) {
      return NextResponse.json(
        { error: 'Winner player not found' },
        { status: 400 }
      )
    }

    // Update the match
    const updatedMatches = updateMatch(bracket.matches, body.matchId, winnerPlayer, body.score)
    
    // Check if bracket is complete
    const isComplete = isBracketComplete(updatedMatches)
    const winner = isComplete ? getBracketWinner(updatedMatches) || undefined : undefined

    // Update bracket in database
    const updateResult = await db.collection<BracketDocument>(COLLECTIONS.BRACKETS)
      .updateOne(
        { _id: new ObjectId(params.id) },
        {
          $set: {
            matches: updatedMatches,
            isComplete,
            winner,
            updatedAt: new Date()
          }
        }
      )

    if (updateResult.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Failed to update bracket' },
        { status: 500 }
      )
    }

    // Return updated bracket
    const updatedBracket = await db.collection<BracketDocument>(COLLECTIONS.BRACKETS)
      .findOne({ _id: new ObjectId(params.id) })

    const response: BracketResponse = {
      id: updatedBracket!._id!.toString(),
      name: updatedBracket!.name,
      type: updatedBracket!.type,
      players: updatedBracket!.players,
      matches: updatedBracket!.matches,
      currentRound: updatedBracket!.currentRound,
      isComplete: updatedBracket!.isComplete,
      winner: updatedBracket!.winner,
      createdAt: updatedBracket!.createdAt!.toISOString(),
      updatedAt: updatedBracket!.updatedAt!.toISOString()
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error updating bracket:', error)
    return NextResponse.json(
      { error: 'Failed to update bracket' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const db = await getDatabase()
    const result = await db.collection<BracketDocument>(COLLECTIONS.BRACKETS)
      .deleteOne({ _id: new ObjectId(params.id) })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Bracket not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Bracket deleted successfully' })
  } catch (error) {
    console.error('Error deleting bracket:', error)
    return NextResponse.json(
      { error: 'Failed to delete bracket' },
      { status: 500 }
    )
  }
}
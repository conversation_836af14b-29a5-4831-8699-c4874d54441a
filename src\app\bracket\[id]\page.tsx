'use client'

import { useEffect, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { BracketResponse, Player } from '@/types/bracket'
import BracketView from '@/components/bracket/BracketView'

export default function BracketDetailPage() {
  const params = useParams()
  const id = params?.id as string
  const router = useRouter()
  const [bracket, setBracket] = useState<BracketResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (id) {
      fetchBracket()
    }
  }, [id])

  const fetchBracket = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/brackets/${id}`)
      
      if (response.ok) {
        const data = await response.json()
        setBracket(data)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to fetch bracket')
      }
    } catch (err) {
      setError('Failed to fetch bracket')
      console.error('Error fetching bracket:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleMatchUpdate = async (
    matchId: string, 
    winner: Player, 
    score?: { player1: number; player2: number }
  ) => {
    if (updating) return

    setUpdating(true)
    try {
      const response = await fetch(`/api/brackets/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          matchId,
          winner: winner.id,
          score
        })
      })

      if (response.ok) {
        const updatedBracket = await response.json()
        setBracket(updatedBracket)
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Failed to update match')
      }
    } catch (err) {
      alert('Failed to update match')
      console.error('Error updating match:', err)
    } finally {
      setUpdating(false)
    }
  }

  const handleDeleteBracket = async () => {
    if (!window.confirm('Are you sure you want to delete this bracket? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/brackets/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        router.push('/')
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Failed to delete bracket')
      }
    } catch (err) {
      alert('Failed to delete bracket')
      console.error('Error deleting bracket:', err)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading bracket...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto p-6 text-center">
          <h2 className="text-xl font-bold mb-4 text-red-600">Error</h2>
          <p className="mb-4">{error}</p>
          <Button onClick={() => router.push('/')}>
            Go Back
          </Button>
        </Card>
      </div>
    )
  }

  if (!bracket) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto p-6 text-center">
          <h2 className="text-xl font-bold mb-4">Bracket Not Found</h2>
          <p className="mb-4">The requested bracket could not be found.</p>
          <Button onClick={() => router.push('/')}>
            Go Back
          </Button>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex justify-between items-center">
        <Button 
          variant="outline" 
          onClick={() => router.push('/')}
        >
          ← Back to Home
        </Button>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={fetchBracket}
            disabled={loading}
          >
            Refresh
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleDeleteBracket}
            className="text-red-600 hover:text-red-700"
          >
            Delete Bracket
          </Button>
        </div>
      </div>

      <BracketView 
        bracket={bracket} 
        onMatchUpdate={handleMatchUpdate}
        isUpdating={updating}
      />
    </div>
  )
}
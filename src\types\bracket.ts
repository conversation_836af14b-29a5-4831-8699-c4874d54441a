import { ObjectId } from 'mongodb'

export type BracketType = 'single' | 'double'
export type MatchStatus = 'pending' | 'in_progress' | 'completed'

export interface Player {
  id: string
  name: string
  seed?: number
}

export interface Match {
  id: string
  round: number
  position: number
  player1?: Player
  player2?: Player
  winner?: Player
  status: MatchStatus
  score?: {
    player1: number
    player2: number
  }
  isWinnersMatch?: boolean
  nextMatchId?: string
  nextMatchPosition?: 'player1' | 'player2'
}

export interface BracketDocument {
  _id?: ObjectId
  name: string
  type: BracketType
  players: Player[]
  matches: Match[]
  currentRound: number
  isComplete: boolean
  winner?: Player
  createdAt?: Date
  updatedAt?: Date
}

export interface BracketResponse {
  id: string
  name: string
  type: BracketType
  players: Player[]
  matches: Match[]
  currentRound: number
  isComplete: boolean
  winner?: Player
  createdAt: string
  updatedAt: string
}

export interface CreateBracketRequest {
  name: string
  type: BracketType
  players: string[]
}

export interface UpdateMatchRequest {
  matchId: string
  winner: string
  score?: {
    player1: number
    player2: number
  }
}
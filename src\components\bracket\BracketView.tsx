'use client'

import { Match, Player, BracketResponse } from '@/types/bracket'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { canStartMatch } from '@/lib/bracket'

interface BracketViewProps {
  bracket: BracketResponse
  onMatchUpdate: (matchId: string, winner: Player, score?: { player1: number; player2: number }) => void
  isUpdating?: boolean
}

export default function BracketView({ bracket, onMatchUpdate, isUpdating }: BracketViewProps) {
  const rounds = Math.max(...bracket.matches.map(m => m.round))
  
  const getMatchesByRound = (round: number, isWinners: boolean = true) => {
    return bracket.matches
      .filter(m => m.round === round && (bracket.type === 'single' || m.isWinnersMatch === isWinners))
      .sort((a, b) => a.position - b.position)
  }

  const handleMatchClick = (match: Match) => {
    if (match.status === 'completed' || !canStartMatch(match)) return

    const winner = window.prompt(
      `Who won the match between ${match.player1?.name} and ${match.player2?.name}?\n\nEnter "1" for ${match.player1?.name} or "2" for ${match.player2?.name}:`
    )

    if (winner === '1' && match.player1) {
      onMatchUpdate(match.id, match.player1)
    } else if (winner === '2' && match.player2) {
      onMatchUpdate(match.id, match.player2)
    }
  }

  const MatchCard = ({ match }: { match: Match }) => (
    <Card 
      className={`p-4 mb-2 cursor-pointer transition-colors border-2 ${
        match.status === 'completed' 
          ? 'bg-green-50 border-green-300 shadow-md' 
          : canStartMatch(match) 
            ? 'bg-blue-50 border-blue-300 hover:bg-blue-100 shadow-md' 
            : 'bg-white border-gray-300 shadow-sm'
      }`}
      onClick={() => handleMatchClick(match)}
    >
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-bold text-gray-800">
            Round {match.round} - Match {match.position}
          </span>
          <span className={`text-xs px-2 py-1 rounded font-semibold ${
            match.status === 'completed' 
              ? 'bg-green-200 text-green-900' 
              : match.status === 'in_progress'
                ? 'bg-yellow-200 text-yellow-900'
                : 'bg-gray-200 text-gray-900'
          }`}>
            {match.status.replace('_', ' ')}
          </span>
        </div>
        
        <div className="space-y-1">
          <div className={`flex justify-between items-center p-3 rounded border ${
            match.winner?.id === match.player1?.id 
              ? 'bg-green-200 border-green-400 text-green-900' 
              : 'bg-gray-100 border-gray-300 text-gray-900'
          }`}>
            <span className="font-bold text-base">
              {match.player1?.name || 'TBD'}
            </span>
            {match.score && (
              <span className="text-base font-bold">
                {match.score.player1}
              </span>
            )}
          </div>
          
          <div className="text-center text-sm font-bold text-gray-700 py-1">VS</div>
          
          <div className={`flex justify-between items-center p-3 rounded border ${
            match.winner?.id === match.player2?.id 
              ? 'bg-green-200 border-green-400 text-green-900' 
              : 'bg-gray-100 border-gray-300 text-gray-900'
          }`}>
            <span className="font-bold text-base">
              {match.player2?.name || 'TBD'}
            </span>
            {match.score && (
              <span className="text-base font-bold">
                {match.score.player2}
              </span>
            )}
          </div>
        </div>
        
        {match.winner && (
          <div className="mt-3 text-center text-base font-bold text-green-800 bg-green-100 rounded p-2">
            🏆 Winner: {match.winner.name}
          </div>
        )}
      </div>
    </Card>
  )

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2 text-gray-900">{bracket.name}</h2>
        <p className="text-lg font-semibold text-gray-700 mb-4">
          {bracket.type === 'single' ? 'Single' : 'Double'} Elimination • {bracket.players.length} Players
        </p>
        
        {bracket.isComplete && bracket.winner && (
          <div className="bg-yellow-200 border-2 border-yellow-400 rounded-lg p-4 mb-4">
            <h3 className="text-xl font-bold text-yellow-900">
              🏆 Tournament Winner: {bracket.winner.name}
            </h3>
          </div>
        )}
      </div>

      {bracket.type === 'single' ? (
        <div className="overflow-x-auto">
          <div className="flex space-x-8 min-w-max">
            {Array.from({ length: rounds }, (_, i) => i + 1).map(round => (
              <div key={round} className="flex-shrink-0 w-80">
                <h3 className="text-xl font-bold mb-4 text-center text-gray-900 bg-gray-100 rounded p-2 border-2 border-gray-300">
                  {round === rounds ? 'Final' : `Round ${round}`}
                </h3>
                <div className="space-y-4">
                  {getMatchesByRound(round).map(match => (
                    <MatchCard key={match.id} match={match} />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-2xl font-bold mb-4 text-center text-gray-900 bg-green-100 rounded p-3 border-2 border-green-300">Winners Bracket</h3>
            <div className="space-y-6">
              {Array.from({ length: rounds }, (_, i) => i + 1).map(round => (
                <div key={round}>
                  <h4 className="text-lg font-bold mb-2 text-gray-900 bg-gray-100 rounded p-2 border border-gray-300">Round {round}</h4>
                  <div className="space-y-2">
                    {getMatchesByRound(round, true).map(match => (
                      <MatchCard key={match.id} match={match} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="text-2xl font-bold mb-4 text-center text-gray-900 bg-red-100 rounded p-3 border-2 border-red-300">Losers Bracket</h3>
            <div className="space-y-6">
              {Array.from({ length: rounds }, (_, i) => i + 1).map(round => (
                <div key={round}>
                  <h4 className="text-lg font-bold mb-2 text-gray-900 bg-gray-100 rounded p-2 border border-gray-300">Round {round}</h4>
                  <div className="space-y-2">
                    {getMatchesByRound(round, false).map(match => (
                      <MatchCard key={match.id} match={match} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <div className="text-center bg-blue-50 border-2 border-blue-200 rounded-lg p-4">
        <p className="text-base font-semibold text-blue-900 mb-1">📋 Instructions:</p>
        <p className="text-sm font-medium text-blue-800">Click on a match to record the winner</p>
        <p className="text-sm font-medium text-blue-800">Matches can only be played when both players are determined</p>
      </div>
    </div>
  )
}
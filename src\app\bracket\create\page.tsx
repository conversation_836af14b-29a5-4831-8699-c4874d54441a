'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BracketType, CreateBracketRequest } from '@/types/bracket'

export default function CreateBracketPage() {
  const router = useRouter()
  const [bracketName, setBracketName] = useState('')
  const [bracketType, setBracketType] = useState<BracketType>('single')
  const [players, setPlayers] = useState<string[]>(['', ''])
  const [isCreating, setIsCreating] = useState(false)

  const addPlayer = () => {
    setPlayers([...players, ''])
  }

  const removePlayer = (index: number) => {
    if (players.length > 2) {
      setPlayers(players.filter((_, i) => i !== index))
    }
  }

  const updatePlayer = (index: number, name: string) => {
    const newPlayers = [...players]
    newPlayers[index] = name
    setPlayers(newPlayers)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const validPlayers = players.filter(p => p.trim())
    if (validPlayers.length < 2) {
      alert('At least 2 players are required')
      return
    }

    if (!bracketName.trim()) {
      alert('Bracket name is required')
      return
    }

    setIsCreating(true)
    
    try {
      const request: CreateBracketRequest = {
        name: bracketName.trim(),
        type: bracketType,
        players: validPlayers
      }

      const response = await fetch('/api/brackets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      })

      if (response.ok) {
        const bracket = await response.json()
        router.push(`/bracket/${bracket.id}`)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create bracket')
      }
    } catch (error) {
      console.error('Error creating bracket:', error)
      alert('Failed to create bracket')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-2xl mx-auto p-6">
        <h1 className="text-3xl font-bold mb-6 text-center">Create Tournament Bracket</h1>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-base font-bold mb-2 text-gray-900">
              Tournament Name
            </label>
            <input
              type="text"
              value={bracketName}
              onChange={(e) => setBracketName(e.target.value)}
              className="w-full px-4 py-3 border-2 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"
              placeholder="Enter tournament name"
              required
            />
          </div>

          <div>
            <label className="block text-base font-bold mb-2 text-gray-900">
              Tournament Type
            </label>
            <Select value={bracketType} onValueChange={(value: BracketType) => setBracketType(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select bracket type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="single">Single Elimination</SelectItem>
                <SelectItem value="double">Double Elimination</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-base font-bold mb-2 text-gray-900">
              Players ({players.filter(p => p.trim()).length})
            </label>
            <div className="space-y-2">
              {players.map((player, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={player}
                    onChange={(e) => updatePlayer(index, e.target.value)}
                    className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 font-medium"
                    placeholder={`Player ${index + 1}`}
                  />
                  {players.length > 2 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => removePlayer(index)}
                      className="px-3 text-red-600 hover:text-red-800 font-bold"
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
              <Button type="button" variant="outline" onClick={addPlayer} className="w-full font-bold">
                Add Player
              </Button>
            </div>
          </div>

          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/')}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isCreating}
              className="flex-1"
            >
              {isCreating ? 'Creating...' : 'Create Bracket'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  )
}
'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { BracketResponse } from '@/types/bracket'

export default function BracketListPage() {
  const router = useRouter()
  const [brackets, setBrackets] = useState<BracketResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBrackets()
  }, [])

  const fetchBrackets = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/brackets')
      
      if (response.ok) {
        const data = await response.json()
        setBrackets(data)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to fetch brackets')
      }
    } catch (err) {
      setError('Failed to fetch brackets')
      console.error('Error fetching brackets:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading brackets...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Tournament Brackets</h1>
        <Button onClick={() => router.push('/bracket/create')}>
          Create New Bracket
        </Button>
      </div>

      {error && (
        <Card className="p-6 mb-6 bg-red-50 border-red-200">
          <p className="text-red-800">{error}</p>
        </Card>
      )}

      {brackets.length === 0 ? (
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-4">No Brackets Yet</h2>
          <p className="text-gray-600 mb-6">
            Create your first tournament bracket to get started!
          </p>
          <Button onClick={() => router.push('/bracket/create')}>
            Create First Bracket
          </Button>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {brackets.map((bracket) => (
            <Card 
              key={bracket.id} 
              className="p-6 cursor-pointer hover:shadow-lg transition-shadow border-2 border-gray-300 hover:border-blue-400"
              onClick={() => router.push(`/bracket/${bracket.id}`)}
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold text-gray-900">{bracket.name}</h3>
                <span className={`px-3 py-1 rounded font-bold text-sm ${
                  bracket.isComplete 
                    ? 'bg-green-200 text-green-900 border border-green-400' 
                    : 'bg-blue-200 text-blue-900 border border-blue-400'
                }`}>
                  {bracket.isComplete ? 'Complete' : 'In Progress'}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-700 font-medium">Type:</span>
                  <span className="font-bold text-gray-900">
                    {bracket.type === 'single' ? 'Single' : 'Double'} Elimination
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-700 font-medium">Players:</span>
                  <span className="font-bold text-gray-900">{bracket.players.length}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-700 font-medium">Current Round:</span>
                  <span className="font-bold text-gray-900">{bracket.currentRound}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-700 font-medium">Created:</span>
                  <span className="font-bold text-gray-900">{formatDate(bracket.createdAt)}</span>
                </div>
              </div>

              {bracket.winner && (
                <div className="mt-4 p-3 bg-yellow-200 border-2 border-yellow-400 rounded">
                  <p className="text-sm font-bold text-yellow-900">
                    🏆 Winner: {bracket.winner.name}
                  </p>
                </div>
              )}

              <div className="mt-4 pt-4 border-t-2 border-gray-200">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-700 font-medium">
                    {bracket.matches.filter(m => m.status === 'completed').length} / {bracket.matches.length} matches complete
                  </span>
                  <span className="text-gray-700 font-medium">
                    Updated: {formatDate(bracket.updatedAt)}
                  </span>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}